<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>参训者 - 首页</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-50">
    <!-- 导航栏 -->
    <nav class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <h1 class="text-xl font-semibold text-gray-900">AI智能培训系统</h1>
                </div>
                <div class="flex items-center space-x-4">
                    <span class="text-gray-700">欢迎，王萌</span>
                    <button onclick="logout()" class="text-red-600 hover:text-red-800">退出</button>
                </div>
            </div>
        </div>
    </nav>

    <div class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <!-- 待办事项提醒 -->
        <div class="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-6">
            <div class="flex">
                <div class="flex-shrink-0">
                    <span class="text-yellow-400">⚠️</span>
                </div>
                <div class="ml-3">
                    <p class="text-sm text-yellow-700">
                        您有 <strong>1项</strong> 新的培训待完成，<strong>1项</strong> 考试即将开始
                    </p>
                </div>
            </div>
        </div>

        <!-- 我的培训任务 -->
        <div class="bg-white shadow overflow-hidden sm:rounded-md mb-6">
            <div class="px-4 py-5 sm:px-6">
                <h3 class="text-lg leading-6 font-medium text-gray-900">我的培训任务</h3>
                <p class="mt-1 max-w-2xl text-sm text-gray-500">当前分配给您的培训项目</p>
            </div>
            <ul class="divide-y divide-gray-200">
                <li class="px-4 py-4 hover:bg-gray-50 cursor-pointer" onclick="window.location.href='training-study.html?id=1'">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="flex-1 min-w-0">
                                <p class="text-sm font-medium text-gray-900">新入职检察官办案指引培训</p>
                                <p class="text-sm text-gray-500">学习截止：2024-01-25 | 考试时间：2024-01-26 14:00-16:00</p>
                            </div>
                        </div>
                        <div class="flex items-center space-x-2">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                待完成
                            </span>
                            <button class="text-blue-600 hover:text-blue-800 text-sm">进入学习</button>
                        </div>
                    </div>
                </li>
                <li class="px-4 py-4 hover:bg-gray-50">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="flex-1 min-w-0">
                                <p class="text-sm font-medium text-gray-900">2024年法规政策学习</p>
                                <p class="text-sm text-gray-500">已完成学习 | 考试成绩：85分</p>
                            </div>
                        </div>
                        <div class="flex items-center space-x-2">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                已完成
                            </span>
                            <button class="text-blue-600 hover:text-blue-800 text-sm">查看成绩</button>
                        </div>
                    </div>
                </li>
            </ul>
        </div>

        <!-- 学习进度统计 -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                                <span class="text-white text-sm">📖</span>
                            </div>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500">总培训项目</dt>
                                <dd class="text-lg font-medium text-gray-900">12</dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                                <span class="text-white text-sm">✅</span>
                            </div>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500">已完成</dt>
                                <dd class="text-lg font-medium text-gray-900">11</dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-yellow-500 rounded-full flex items-center justify-center">
                                <span class="text-white text-sm">📊</span>
                            </div>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500">平均成绩</dt>
                                <dd class="text-lg font-medium text-gray-900">87.5分</dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="js/auth.js"></script>
    <script>
        const user = checkAuth();
        if (!user || user.role !== 'trainee') {
            window.location.href = 'index.html';
        }
    </script>
</body>
</html>