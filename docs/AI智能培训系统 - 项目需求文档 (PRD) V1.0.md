### AI智能培训系统 - 项目需求文档 (PRD) V1.0

------

### 1. 项目概述 (Project Overview)

本项目旨在为南宁某检察院政治部开发一个现代化的“AI智能培训系统”。系统的核心目标是将传统的、手动的线下培训流程迁移至线上，并通过引入人工智能（AI）技术，实现培训内容生成、考试组织、智能评估和数据分析的自动化与智能化。

该系统将服务于检察院内部的各类培训需求，特别是针对新入职助理检察官的办案基础指导和全体员工的年度政策法规学习。它将赋能培训组织者，使其能够根据具体的培训目标和提供的学习资料，快速生成高质量的培训计划、配套题库和在线考试，从而显著提升培训效率、效果和管理的精细化水平。

### 2. 项目范围 (Project Scope)

#### **2.1 范围内 (In-Scope)**

- **全流程培训管理：** 支持从培训项目的创建、规划、多阶段设计到最终发布的全生命周期管理。
- **AI辅助内容生成：** 基于上传的文本材料（如Word, PDF），利用检索增强生成（RAG）技术，智能生成配套的考试题目。
- **题库建设与管理：** 包括AI生成题目的强制人工审核、手动创建题目、通过Excel模板批量导入题目，以及对题库的分类管理。
- **灵活的考试组织：** 支持手动或按规则从题库中抽取题目，灵活组建试卷，并可设定考试时间、时长、分值等参数。
- **定向精准发布：** 能够将培训和考试精准地指派给特定部门、特定标签或手动选择的员工。
- **在线考试功能：** 提供限时作答、答案自动保存、中断后可继续作答（时间不补）的稳定考试环境。
- **混合评分机制：** 客观题（选择、判断等）由系统自动评分，主观题（问答题等）由指定的“评卷人”进行人工在线评分。
- **数据统计与分析：** 提供成绩排名、平均分、及格率等基础统计报表，并包含高频错题分析功能。
- **角色与权限系统：** 包含系统管理员、培训发起者、评卷人、参训者四种核心角色，并进行严格的权限隔离。
- **学习材料多样化：** 支持上传文本（Word, PDF, TXT）及视频文件作为学习阶段的材料。

#### **2.2 范围外 (Out-of-Scope)**

- **主观题AI自动评分：** AI对问答题的评分功能被视为远期目标，V1.0版本不包含此功能。
- **AI自适应学习路径推荐：** 系统不提供基于用户画像的个性化学习内容智能推荐。所谓的“个性化”是指由发起者进行“定向指派”。
- **题库自动更新与淘汰：** 系统不会主动扫描法规变化并自动更新或废弃题库中的旧题目。题库的维护与培训发起者的具体操作强相关。
- **与外部系统集成：** V1.0版本不包含与人事、绩效、OA等外部系统的接口打通。

### 3. 目标用户画像 (User Personas)

- **张科长 (培训发起者):**
  - **身份:** 政治部负责人，40-50岁。
  - **工作:** 工作繁忙，负责规划和组织全院的各类培训任务。
  - **痛点:** 线下培训组织流程繁琐，出题、组卷、批改耗费大量人力，培训效果难以量化。
  - **期望:** 希望系统能极大提升工作效率，通过AI快速生成高质量题目，轻松组织考试，并能直观地看到培训结果的统计分析。他非常看重内容的准确性和流程的可控性。
- **王萌 (参训者):**
  - **身份:** 新入职助理检察官，25-30岁。
  - **工作:** 处于业务学习和能力提升的关键期。
  - **痛点:** 传统学习方式被动，缺乏针对性练习和及时的反馈。
  - **期望:** 希望能清晰地看到自己的学习任务，随时随地进行学习和自测。考试后能及时获得反馈，了解自己的薄弱环节，从而进行针对性巩固。
- **李老师 (评卷人):**
  - **身份:** 资深检察官，被指定为某次考试的评卷人。
  - **工作:** 业务专家，负责对专业性强的问答题进行评分。
  - **痛点:** 传统纸质批卷效率低，分数统计易出错。
  - **期望:** 希望能有一个简洁的在线评分界面，可以方便地为每道主观题打分和写评语，系统能自动汇总分数。

### 4. 用户流程 (User Flow)

#### **4.1 培训发起者 - 创建一次完整的培训**

1. **登录与访问：** 张科长登录系统，进入“培训管理”模块。首页展示他创建过的所有培训项目列表（含状态）。

2. **创建培训项目：** 点击“发起新培训”，填写`培训名称`和`培训目标`。

3. 设计培训阶段：

    在新创建的培训项目中，点击“添加阶段”。

   - **添加学习阶段：** 选择阶段类型为“学习”，上传相关的Word、PDF或视频文件作为学习资料，并设定学习要求和截止日期。
   - **添加考试阶段：** 选择阶段类型为“考试”，并设定依赖于前一个学习阶段。

4. AI生成与审核题目：

   - 进入“题库管理”，选择刚才上传的文本资料，指令AI“根据此材料生成20道选择题和5道问答题，归入‘新入职检察官’题库”。
   - 系统生成题目后，进入“题目审核”界面。张科长逐一审核AI生成的题目，界面同时展示题目、答案和原文出处。他对部分题目进行微调后点击“通过”，题目正式入库。
   - 同时，他通过“Excel导入”功能，将一个已有的题库文件批量导入系统。

5. 创建与发布考试：

   - 回到“考试阶段”，点击“创建考试”。
   - 通过“规则抽题”方式，设定“从‘新入职检察官’题库中随机抽取15道选择题，从‘通用法规’题库中手动挑选5道问答题”，并设定各题型分值。
   - 配置考试的起止时间、答题时长，并指定“评卷人”为李老师。
   - 最后，在“培训对象”管理中，通过按“2025年入职”标签筛选，将本次培训指派给所有新员工。

6. **监控与评估：** 考试结束后，系统自动判完客观题。李老师完成主观题评分。张科长在“报表中心”查看本次考试的成绩排名、及格率和高频错题分析报告，评估培训效果。

#### **4.2 参训者 - 参与一次培训**

1. **接收任务：** 王萌登录系统，首页卡片式待办事项提醒她“您有一项新的‘新入职检察官办案指引’培训待完成”。
2. **在线学习：** 她点击进入培训，首先完成“学习阶段”，在线阅读了指定的PDF文件，并观看了教学视频。
3. **参加考试：** 在规定的考试时间内，她点击进入“考试阶段”，系统显示考试须知和倒计时。答题过程中，她可以标记不确定的题目。
4. **异常处理：** 答题中途，她的电脑突然蓝屏重启。重新登录系统后，她发现可以从刚才中断的题目继续作答，已完成的答案均被保留，但考试剩余时间没有增加。
5. **交卷与查看结果：** 完成所有题目后，她点击“交卷”。几天后，她收到系统消息通知成绩已发布。她再次登录，查看了自己的分数、每道题的对错情况以及正确答案解析，重点复习了自己答错的题目。

### 5. 核心功能详述 (Detailed Core Features)

- **5.1 培训管理模块**
  - **5.1.1 培训项目列表：** 以列表形式展示所有培训，包含名称、状态（草稿、进行中、已结束）、创建时间、参与人数等信息。支持搜索和筛选。
  - **5.1.2 培训创建/编辑：** 提供表单用于填写培训基础信息。支持从已有培训克隆，快速创建相似的培训项目。
  - **5.1.3 阶段化设计器：** 提供一个可视化的界面，允许发起者通过拖拽或点击添加“学习”和“考试”两种类型的阶段，并可调整它们的顺序。
- **5.2 内容与题库模块**
  - **5.2.1 资料库：** 集中管理所有上传的学习资料，支持按文件夹或标签分类。一份资料可被多个培训引用。
  - **5.2.2 AI题目生成器：** 界面上选择源材料，选择题库，输入生成要求（题型、数量），点击生成。后台异步处理，完成后通知用户。
  - **5.2.3 题目审核工作台：** 为审核人员设计的专用界面，三栏布局（题目列表、题目详情、原文定位），提供高效的审核和编辑体验。
  - **5.2.4 题库浏览器：** 允许用户浏览、搜索、编辑、删除所有已入库的题目。
- **5.3 考试与评估模块**
  - **5.3.1 试卷编辑器：** 左右分栏布局，左侧为题库浏览器，右侧为试卷内容。支持从左侧拖拽或勾选题目录入右侧试卷。可实时显示试卷总分。
  - **5.3.2 评卷人工作台：** 评卷人登录后，可看到分配给自己的评卷任务。界面清晰展示待评分的答卷和主观题作答内容，提供打分框和评语框。
  - **5.3.3 统计报表：** 报表应为可视化图表和数据表格的结合。支持数据导出为Excel。
- **5.4 用户门户模块**
  - **5.4.1 参训者仪表盘：** 采用响应式卡片布局，每张卡片代表一个培训或考试任务，用颜色和图标区分状态，并明确标出截止日期。
  - **5.4.2 在线考试播放器：** 简洁、无干扰的考试界面。顶部固定显示考生信息、倒计时和交卷按钮。题目列表导航栏可让考生快速跳转和查看标记状态。



```mermaid
graph TD
    %% 1. 样式定义 Style Definitions
    %% 定义四种角色的背景色和一种决策节点的颜色
    classDef initiator fill:#e3f2fd,stroke:#1565c0,stroke-width:2px;
    classDef trainee fill:#e8f5e9,stroke:#2e7d32,stroke-width:2px;
    classDef reviewer fill:#fffde7,stroke:#a47c00,stroke-width:2px;
    classDef system fill:#f3e5f5,stroke:#6a1b9a,stroke-width:1px,stroke-dasharray: 5 5;
    classDef decision fill:#ffebee,stroke:#c62828,stroke-width:2px;

    %% 2. 节点与流程定义 Node and Flow Definitions
    subgraph 培训发起者
        A[登录系统] --> B进入培训管理模块;
        B --> C{发起新培训};
        C --> D[1.填写培训基本信息名称目标];
        D --> E[2.设计培训阶段];
        E --> E1添加学习阶段:上传资料;
        E1 --> F{如何生成题目};
        F -- AI智能生成 --> G[指令AI根据资料生成题目];
        G --> H[进入审核工作台];
        H --> I{审核结果?};
        I -- 通过编辑后通过 --> K[题目正式入库];
        I -- 驳回 --> H;
        F -- 手动批量导入 --> J[手动创建或Excel导入题目];
        J --> K;
        E --> E2添加考试阶段;
        K --> L[3.创建考试活动];
        E2 --> L;
        L --> M{组卷方式?};
        M -- 手动选题 --> N[手动从题库选题组卷];
        M -- 规则抽题 --> O[按规则随机抽题组卷];
        N --> P[4.配置考试参数 时间, 分值];
        O --> P;
        P --> Q[5.指定评卷人培训对象];
        Q --> R[6.正式发布培训];
        R --> S监控培训与考试进度;
        S --> T[8.查看最终统计报表-含主观题成绩];
    end
    

    %% 4. 样式应用 Applying Styles
    %% 将定义好的样式应用到对应的节点
    class A,B,C,D,E,E1,E2,F,G,H,I,J,K,L,M,N,O,P,Q,R,S,T initiator;
    class AA,BB,CC,DD,EE,FF,GG,HH,II,JJ,KK,LL,MM,NN,OO trainee;
    class RR1,RR2,RR3,RR4,RR5 reviewer;
    class SYS1,SYS2 system;
    class C,F,I,M,GG decision;
```



```mermaid
graph TD
    %% 1. 样式定义 Style Definitions
    %% 定义四种角色的背景色和一种决策节点的颜色
    classDef initiator fill:#e3f2fd,stroke:#1565c0,stroke-width:2px;
    classDef trainee fill:#e8f5e9,stroke:#2e7d32,stroke-width:2px;
    classDef reviewer fill:#fffde7,stroke:#a47c00,stroke-width:2px;
    classDef system fill:#f3e5f5,stroke:#6a1b9a,stroke-width:1px,stroke-dasharray: 5 5;
    classDef decision fill:#ffebee,stroke:#c62828,stroke-width:2px;

    subgraph 参训者 Trainee
        AA[登录系统] --> BB查看个人仪表盘-我的任务;
        BB --> CC[进入指定培训];
        CC --> DD完成学习阶段-阅读资料看视频;
        DD --> EE[进入在线考试];
        EE --> FF{考试中...};
        FF --> GG{发生意外中断?};
        GG -- 是 --> HH[重新登录系统];
        HH --> II[从中断处继续答题 时间不补];
        GG -- 否 --> JJ[完成所有题目];
        II --> JJ;
        JJ --> KK[主动交卷/时间到自动交卷];
        KK --> LL等待成绩公布;
        LL --> MM[收到成绩发布通知];
        MM --> NN[查看分数、答案和解析];
        NN --> OO复盘学习;
    end

    %% 4. 样式应用 Applying Styles
    %% 将定义好的样式应用到对应的节点
    class A,B,C,D,E,E1,E2,F,G,H,I,J,K,L,M,N,O,P,Q,R,S,T initiator;
    class AA,BB,CC,DD,EE,FF,GG,HH,II,JJ,KK,LL,MM,NN,OO trainee;
    class RR1,RR2,RR3,RR4,RR5 reviewer;
    class SYS1,SYS2 system;
    class C,F,I,M,GG decision;
```



### 6. 技术栈与架构 (Tech Stack & Architecture)

#### **6.1 技术栈**

- **数据库:** PostgreSQL 或 MySQL，两者均为成熟可靠的关系型数据库。

- AI核心 - RAG方案:

  - **文档加载与切分:** LangChain 或 LlamaIndex 框架。

  - **向量化模型 (Embedding):** 选用高性能的中文向量化模型，如 BGE-large-zh, M3E-large 等。

  - **向量数据库:** FAISS (适用于中小型数据，集成简单) 或 Milvus (适用于大规模数据，功能更强大)。

  - 大型语言模型 (LLM):

     根据部署环境决定。

    - **本地化部署方案:** 选用优秀的开源模型，如 Qwen (通义千问), ChatGLM, Baichuan 等，进行本地私有化部署。

#### **6.2 架构设计总览

系统将围绕以下六个核心模块构建，它们各自负责一个明确的业务领域，并通过定义良好的接口（API）进行协作。

```mermaid
graph TD
    subgraph "核心业务模块"
        M1["模块一：用户与权限中心 (IAM Core)"]
        M2["模块二：培训管理模块 (Training Management)"]
        M3["模块三：智能内容与题库模块 (Content & Question Bank)"]
        M4["模块四：在线考试与评估模块 (Exam & Assessment)"]
        M5["模块五：评分与报表模块 (Scoring & Reporting)"]
        M6["模块六：通知服务 (Notification Service)"]
    end

    M2 -- "1.请求验证用户身份" --> M1
    M2 -- "2 关联学习资料" --> M3
    M3 -- "3.提供审核后的题目" --> M4
    M2 -- "4创建考试任务" --> M4
    M4 -- "5.提交待评分答卷" --> M5
    M5 -- "6.请求评卷人身份" --> M1
    M5 -- "7.生成报表" --> M2
    M2 -- "8.触发培训通知" --> M6
    M4 -- "9.触发考试提醒" --> M6
    M5 -- "10.触发成绩发布通知" --> M6

    classDef core fill:#e3f2fd,stroke:#1565c0,stroke-width:2px;
    classDef support fill:#e8f5e9,stroke:#2e7d32,stroke-width:2px;
    class M1,M2,M3,M4,M5 core;
    class M6 support;
```

------

##### 模块一：用户与权限中心 (Identity & Access Management Core)

这是整个系统的基石，负责管理“谁能做什么”。

- 核心职责:
  - **身份认证 (Authentication):** 处理所有用户的登录、登出、会话管理。确保每个进入系统的请求都来自于一个合法的、已认证的用户。
  - **角色管理 (Role Management):** 定义系统中的所有角色（系统管理员、培训发起者、评卷人、参训者），并支持对角色的增删改查。
  - **权限授权 (Authorization):** 管理角色与权限的映射关系。当其他模块接收到一个操作请求时（如“创建培训”），它会向本模块查询：“当前用户是否有权限执行此操作？”
  - **用户与组织架构管理:** 维护检察院的部门结构和员工列表，支持用户的个人信息管理。

------

##### 模块二：培训管理模块 (Training Management)

这是业务流程的“总指挥”，负责将零散的学习和考试活动组织成一个完整的培训项目。

- 核心职责:
  - **培训项目生命周期管理:** 负责培训项目的创建、编辑、发布、归档和克隆。一个“培训项目”是所有相关活动（学习、考试）的顶层容器。
  - **阶段化流程编排:** 提供让培训发起者能够设计多阶段流程（如：学习A -> 考试A -> 学习B -> 综合考试）的核心功能。
  - **对象关系管理:** 负责维护“培训项目”与“参训人员”、“培训项目”与“学习资料”、“培训项目”与“考试活动”之间的关联关系。它决定了谁应该参与哪个培训。
  - **状态跟踪与视图:** 从宏观视角跟踪整个培训项目的进度，并为不同角色提供相应的项目视图。

------

##### 模块三：智能内容与题库模块 (Content & Question Bank)

这是系统的“大脑”，负责所有与内容处理、AI生成和题库相关的核心智能功能。

- 核心职责:
  - **资料库管理:** 统一管理所有上传的学习资料（PDF, Word, 视频等），提供存储、分类和检索功能。
  - **RAG核心流程:** 封装完整的检索增强生成（RAG）管线。包括：文档的加载与智能切块、文本向量化、与向量数据库的交互、构建提示词（Prompt）、以及与大型语言模型（LLM）的通信。
  - **题目审核工作流:** 管理AI生成题目的“待审核”状态，并提供强制人工审核的界面逻辑，确保每一道题的质量都经过人类确认。
  - **题库(Question Bank)管理:** 对所有审核通过的题目进行持久化存储。提供对题目的增、删、改、查功能，支持按知识点、难度、题型等维度进行分类和检索，并包含Excel批量导入功能。

------

##### 模块四：在线考试与评估模块 (Exam & Assessment)

这是业务流程中的“事件执行者”，负责管理每一次具体的、有时间限制的考试活动。

- 核心职责:
  - **组卷与试卷管理:** 提供从“题库模块”中抽取题目（手动或按规则）来创建一份具体试卷的功能。
  - **考试实例管理:** 负责创建一次具体的考试活动，包括设定起止时间、答题时长、参与人员名单等。
  - **在线考试引擎:** 为参训者提供稳定、可靠的在线考试界面。核心是处理实时倒计时、答案的暂存与自动保存、以及处理网络中断后的断点续考逻辑。
  - **答卷提交与管理:** 负责在考试结束后收集所有参训者的答卷，并将其状态标记为“待评分”。

------

##### 模块五：评分与报表模块 (Scoring & Reporting)

这是业务流程的“成果分析师”，负责对考试结果进行量化、分析和呈现。

- 核心职责:
  - **自动评分服务:** 对答卷中的客观题（选择、判断等）进行自动、即时的评分。
  - **人工评卷工作台:** 为“评卷人”角色提供一个专用的工作界面，使其可以对分配给自己的主观题进行在线打分和添加评语。
  - **成绩聚合与计算:** 汇总一份答卷的客观题得分和主观题得分，计算出最终总分。
  - **数据分析与报表生成:** 基于所有考生的成绩数据，生成多维度的统计报表，如成绩排名、平均分、及格率、高频错题分析等，并支持数据导出。

------

##### 模块六：通知服务 (Notification Service)

这是一个通用的支撑模块，负责系统内外的消息触达。

- 核心职责:
  - **消息模板管理:** 管理不同场景下的通知消息模板（如：新培训通知、考试即将开始提醒、成绩发布通知等）。
  - **消息分发:** 提供一个统一的API接口，供其他业务模块调用。它接收消息内容和目标用户，并负责将消息通过指定渠道（如系统内站内信、未来可能扩展的邮件或短信）发送出去。
  - **消息记录:** 记录所有已发送消息的历史，便于查询和审计。