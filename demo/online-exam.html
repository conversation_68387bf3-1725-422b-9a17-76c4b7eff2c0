<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>在线考试</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-50">
    <!-- 考试头部信息 -->
    <div class="bg-white shadow-sm border-b sticky top-0 z-10">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <div class="flex items-center space-x-4">
                    <h1 class="text-lg font-semibold text-gray-900">新入职检察官办案指引考试</h1>
                    <span class="text-sm text-gray-500">考生：王萌</span>
                </div>
                <div class="flex items-center space-x-4">
                    <div class="text-sm text-gray-500">
                        剩余时间：<span id="timer" class="font-medium text-red-600">01:58:30</span>
                    </div>
                    <button onclick="submitExam()" class="bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700">
                        交卷
                    </button>
                </div>
            </div>
        </div>
    </div>

    <div class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div class="grid grid-cols-4 gap-6">
            <!-- 左侧：题目导航 -->
            <div class="col-span-1">
                <div class="bg-white shadow sm:rounded-lg sticky top-24">
                    <div class="px-4 py-5 sm:p-6">
                        <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">题目导航</h3>
                        
                        <div class="mb-4">
                            <h4 class="text-sm font-medium text-gray-700 mb-2">选择题 (1-15)</h4>
                            <div class="grid grid-cols-5 gap-1">
                                <button onclick="goToQuestion(1)" class="w-8 h-8 text-xs border rounded bg-green-100 text-green-800">1</button>
                                <button onclick="goToQuestion(2)" class="w-8 h-8 text-xs border rounded bg-green-100 text-green-800">2</button>
                                <button onclick="goToQuestion(3)" class="w-8 h-8 text-xs border rounded bg-blue-100 text-blue-800">3</button>
                                <button onclick="goToQuestion(4)" class="w-8 h-8 text-xs border rounded hover:bg-gray-100">4</button>
                                <button onclick="goToQuestion(5)" class="w-8 h-8 text-xs border rounded hover:bg-gray-100">5</button>
                                <button onclick="goToQuestion(6)" class="w-8 h-8 text-xs border rounded hover:bg-gray-100">6</button>
                                <button onclick="goToQuestion(7)" class="w-8 h-8 text-xs border rounded hover:bg-gray-100">7</button>
                                <button onclick="goToQuestion(8)" class="w-8 h-8 text-xs border rounded hover:bg-gray-100">8</button>
                                <button onclick="goToQuestion(9)" class="w-8 h-8 text-xs border rounded hover:bg-gray-100">9</button>
                                <button onclick="goToQuestion(10)" class="w-8 h-8 text-xs border rounded hover:bg-gray-100">10</button>
                                <button onclick="goToQuestion(11)" class="w-8 h-8 text-xs border rounded hover:bg-gray-100">11</button>
                                <button onclick="goToQuestion(12)" class="w-8 h-8 text-xs border rounded hover:bg-gray-100">12</button>
                                <button onclick="goToQuestion(13)" class="w-8 h-8 text-xs border rounded hover:bg-gray-100">13</button>
                                <button onclick="goToQuestion(14)" class="w-8 h-8 text-xs border rounded hover:bg-gray-100">14</button>
                                <button onclick="goToQuestion(15)" class="w-8 h-8 text-xs border rounded hover:bg-gray-100">15</button>
                            </div>
                        </div>

                        <div class="mb-4">
                            <h4 class="text-sm font-medium text-gray-700 mb-2">问答题 (16-20)</h4>
                            <div class="grid grid-cols-5 gap-1">
                                <button onclick="goToQuestion(16)" class="w-8 h-8 text-xs border rounded hover:bg-gray-100">16</button>
                                <button onclick="goToQuestion(17)" class="w-8 h-8 text-xs border rounded hover:bg-gray-100">17</button>
                                <button onclick="goToQuestion(18)" class="w-8 h-8 text-xs border rounded hover:bg-gray-100">18</button>
                                <button onclick="goToQuestion(19)" class="w-8 h-8 text-xs border rounded hover:bg-gray-100">19</button>
                                <button onclick="goToQuestion(20)" class="w-8 h-8 text-xs border rounded hover:bg-gray-100">20</button>
                            </div>
                        </div>

                        <div class="text-xs text-gray-500 space-y-1">
                            <div class="flex items-center">
                                <div class="w-3 h-3 bg-green-100 border rounded mr-2"></div>
                                <span>已答题</span>
                            </div>
                            <div class="flex items-center">
                                <div class="w-3 h-3 bg-blue-100 border rounded mr-2"></div>
                                <span>当前题</span>
                            </div>
                            <div class="flex items-center">
                                <div class="w-3 h-3 bg-white border rounded mr-2"></div>
                                <span>未答题</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 右侧：题目内容 -->
            <div class="col-span-3">
                <div class="bg-white shadow sm:rounded-lg">
                    <div class="px-4 py-5 sm:p-6">
                        <div class="flex justify-between items-center mb-6">
                            <h3 class="text-lg leading-6 font-medium text-gray-900">第3题 / 共20题</h3>
                            <div class="flex items-center space-x-2">
                                <button onclick="markQuestion()" class="text-sm text-blue-600 hover:text-blue-800">标记</button>
                                <span class="text-sm text-gray-500">5分</span>
                            </div>
                        </div>

                        <div id="questionContent" class="mb-6">
                            <div class="mb-6">
                                <p class="text-base text-gray-900 mb-4">
                                    根据《人民检察院刑事诉讼规则》，检察官在审查起诉阶段应当重点审查哪些内容？
                                </p>
                                
                                <div class="space-y-3">
                                    <label class="flex items-start">
                                        <input type="radio" name="answer" value="A" class="mt-1 mr-3">
                                        <span class="text-sm">A. 仅审查犯罪事实是否清楚</span>
                                    </label>
                                    <label class="flex items-start">
                                        <input type="radio" name="answer" value="B" class="mt-1 mr-3">
                                        <span class="text-sm">B. 仅审查证据是否确实充分</span>
                                    </label>
                                    <label class="flex items-start">
                                        <input type="radio" name="answer" value="C" class="mt-1 mr-3">
                                        <span class="text-sm">C. 仅审查适用法律是否正确</span>
                                    </label>
                                    <label class="flex items-start">
                                        <input type="radio" name="answer" value="D" class="mt-1 mr-3" checked>
                                        <span class="text-sm">D. 全面审查犯罪事实、证据、适用法律等各个方面</span>
                                    </label>
                                </div>
                            </div>
                        </div>

                        <div class="flex justify-between">
                            <button onclick="prevQuestion()" class="bg-gray-300 text-gray-700 px-6 py-2 rounded-md hover:bg-gray-400">
                                上一题
                            </button>
                            <button onclick="saveAndNext()" class="bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700">
                                保存并下一题
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 交卷确认模态框 -->
    <div id="submitModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden">
        <div class="flex items-center justify-center min-h-screen">
            <div class="bg-white rounded-lg p-6 max-w-md w-full mx-4">
                <h3 class="text-lg font-medium text-gray-900 mb-4">确认交卷</h3>
                <p class="text-sm text-gray-500 mb-6">
                    您还有 <span id="remainingTime">1小时58分钟</span> 考试时间，确定要提前交卷吗？
                    <br><br>
                    答题情况：已答 <span class="text-green-600">2题</span>，未答 <span class="text-red-600">18题</span>
                </p>
                <div class="flex justify-end space-x-3">
                    <button onclick="closeSubmitModal()" class="bg-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-400">
                        继续答题
                    </button>
                    <button onclick="confirmSubmit()" class="bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700">
                        确认交卷
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="js/auth.js"></script>
    <script>
        let currentQuestion = 3;
        let totalQuestions = 20;
        let timeLeft = 7110; // 1小时58分30秒

        // 倒计时
        function updateTimer() {
            const hours = Math.floor(timeLeft / 3600);
            const minutes = Math.floor((timeLeft % 3600) / 60);
            const seconds = timeLeft % 60;
            
            document.getElementById('timer').textContent = 
                `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
            
            if (timeLeft <= 0) {
                alert('考试时间到，系统将自动交卷！');
                confirmSubmit();
                return;
            }
            
            timeLeft--;
        }

        // 启动倒计时
        setInterval(updateTimer, 1000);

        function goToQuestion(questionNum) {
            currentQuestion = questionNum;
            // 模拟切换题目
            console.log('切换到第', questionNum, '题');
        }

        function prevQuestion() {
            if (currentQuestion > 1) {
                currentQuestion--;
                goToQuestion(currentQuestion);
            }
        }

        function saveAndNext() {
            // 保存当前答案
            saveCurrentAnswer();
            
            if (currentQuestion < totalQuestions) {
                currentQuestion++;
                goToQuestion(currentQuestion);
            }
        }

        function saveCurrentAnswer() {
            // 模拟保存答案到本地存储
            const answer = document.querySelector('input[name="answer"]:checked')?.value;
            if (answer) {
                localStorage.setItem(`question_${currentQuestion}`, answer);
                console.log('保存第', currentQuestion, '题答案:', answer);
            }
        }

        function markQuestion() {
            alert('题目已标记');
        }

        function submitExam() {
            document.getElementById('submitModal').classList.remove('hidden');
        }

        function closeSubmitModal() {
            document.getElementById('submitModal').classList.add('hidden');
        }

        function confirmSubmit() {
            // 保存当前答案
            saveCurrentAnswer();
            
            alert('交卷成功！请等待成绩公布。');
            window.location.href = 'trainee-dashboard.html';
        }

        // 页面离开前保存答案
        window.addEventListener('beforeunload', function(e) {
            saveCurrentAnswer();
        });

        // 定期自动保存
        setInterval(saveCurrentAnswer, 30000); // 每30秒自动保存
    </script>
</body>
</html>