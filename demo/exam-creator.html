<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>考试创建器</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-50">
    <nav class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <h1 class="text-xl font-semibold text-gray-900">考试创建器</h1>
                </div>
                <div class="flex items-center space-x-4">
                    <button onclick="window.location.href='training-management.html'" class="text-gray-600 hover:text-gray-800">返回</button>
                    <span class="text-gray-700">张科长</span>
                    <button onclick="logout()" class="text-red-600 hover:text-red-800">退出</button>
                </div>
            </div>
        </div>
    </nav>

    <div class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- 左侧：题库浏览器 -->
            <div class="bg-white shadow sm:rounded-lg">
                <div class="px-4 py-5 sm:p-6">
                    <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">题库浏览器</h3>
                    
                    <div class="mb-4 space-y-2">
                        <select class="w-full border-gray-300 rounded-md">
                            <option>新入职检察官题库 (45题)</option>
                            <option>通用法规题库 (120题)</option>
                            <option>案例分析题库 (30题)</option>
                        </select>
                        <div class="flex space-x-2">
                            <button onclick="setQuestionType('choice')" class="flex-1 bg-blue-100 text-blue-800 px-3 py-1 rounded text-sm">选择题 (25)</button>
                            <button onclick="setQuestionType('judge')" class="flex-1 bg-green-100 text-green-800 px-3 py-1 rounded text-sm">判断题 (15)</button>
                            <button onclick="setQuestionType('essay')" class="flex-1 bg-purple-100 text-purple-800 px-3 py-1 rounded text-sm">问答题 (5)</button>
                        </div>
                    </div>
                    
                    <div class="space-y-3 max-h-96 overflow-y-auto">
                        <div class="border rounded-lg p-3 hover:bg-gray-50 cursor-pointer" onclick="addToExam(this)">
                            <div class="flex justify-between items-start">
                                <div class="flex-1">
                                    <p class="text-sm font-medium text-gray-900">根据《人民检察院刑事诉讼规则》，检察官在审查起诉阶段应当重点审查哪些内容？</p>
                                    <p class="text-xs text-gray-500 mt-1">选择题 | 难度：中等</p>
                                </div>
                                <button class="ml-2 text-blue-600 hover:text-blue-800 text-sm">+</button>
                            </div>
                        </div>
                        
                        <div class="border rounded-lg p-3 hover:bg-gray-50 cursor-pointer" onclick="addToExam(this)">
                            <div class="flex justify-between items-start">
                                <div class="flex-1">
                                    <p class="text-sm font-medium text-gray-900">检察官在办案过程中应当遵循哪些基本原则？</p>
                                    <p class="text-xs text-gray-500 mt-1">问答题 | 难度：困难</p>
                                </div>
                                <button class="ml-2 text-blue-600 hover:text-blue-800 text-sm">+</button>
                            </div>
                        </div>
                        
                        <div class="border rounded-lg p-3 hover:bg-gray-50 cursor-pointer" onclick="addToExam(this)">
                            <div class="flex justify-between items-start">
                                <div class="flex-1">
                                    <p class="text-sm font-medium text-gray-900">人民检察院对刑事案件的管辖权包括哪些情形？</p>
                                    <p class="text-xs text-gray-500 mt-1">选择题 | 难度：简单</p>
                                </div>
                                <button class="ml-2 text-blue-600 hover:text-blue-800 text-sm">+</button>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mt-4 p-3 bg-gray-50 rounded-lg">
                        <h4 class="font-medium text-gray-900 mb-2">智能抽题规则</h4>
                        <div class="space-y-2">
                            <div class="flex items-center space-x-2">
                                <input type="number" placeholder="数量" class="w-16 border-gray-300 rounded text-sm">
                                <span class="text-sm">道</span>
                                <select class="flex-1 border-gray-300 rounded text-sm">
                                    <option>选择题</option>
                                    <option>判断题</option>
                                    <option>问答题</option>
                                </select>
                                <button class="bg-green-600 text-white px-2 py-1 rounded text-sm">添加</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 右侧：试卷编辑器 -->
            <div class="bg-white shadow sm:rounded-lg">
                <div class="px-4 py-5 sm:p-6">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-lg leading-6 font-medium text-gray-900">试卷编辑器</h3>
                        <div class="text-sm text-gray-500">
                            总分：<span id="totalScore" class="font-medium text-gray-900">0</span>分
                        </div>
                    </div>
                    
                    <div class="mb-4 grid grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700">考试名称</label>
                            <input type="text" class="mt-1 w-full border-gray-300 rounded-md" value="新入职检察官办案指引考试">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">考试时长(分钟)</label>
                            <input type="number" class="mt-1 w-full border-gray-300 rounded-md" value="120">
                        </div>
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700">评卷人</label>
                        <select class="mt-1 w-full border-gray-300 rounded-md">
                            <option>李老师 (资深检察官)</option>
                            <option>王主任 (业务专家)</option>
                            <option>陈科长 (法律顾问)</option>
                        </select>
                    </div>
                    
                    <div class="border-2 border-dashed border-gray-300 rounded-lg p-4 mb-4">
                        <div id="examQuestions" class="space-y-3">
                            <div class="text-center text-gray-500">
                                <span class="text-4xl">📝</span>
                                <p class="mt-2">从左侧题库中选择题目添加到试卷</p>
                                <p class="text-sm">或使用智能抽题规则自动组卷</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700">培训对象</label>
                            <div class="mt-2 space-y-2">
                                <label class="flex items-center">
                                    <input type="checkbox" class="rounded border-gray-300">
                                    <span class="ml-2 text-sm">按标签筛选：2025年入职员工</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="checkbox" class="rounded border-gray-300">
                                    <span class="ml-2 text-sm">按部门筛选：政治部</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="checkbox" class="rounded border-gray-300">
                                    <span class="ml-2 text-sm">手动选择人员</span>
                                </label>
                            </div>
                        </div>
                        
                        <div class="grid grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700">考试开始时间</label>
                                <input type="datetime-local" class="mt-1 w-full border-gray-300 rounded-md">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700">考试结束时间</label>
                                <input type="datetime-local" class="mt-1 w-full border-gray-300 rounded-md">
                            </div>
                        </div>
                        
                        <div class="flex justify-end space-x-2">
                            <button class="bg-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-400">
                                保存草稿
                            </button>
                            <button onclick="publishExam()" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">
                                发布考试
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="js/auth.js"></script>
    <script>
        let examQuestions = [];
        let totalScore = 0;

        function addToExam(element) {
            const questionText = element.querySelector('.text-sm.font-medium').textContent;
            const questionType = element.querySelector('.text-xs').textContent.split('|')[0].trim();
            
            const questionDiv = document.createElement('div');
            questionDiv.className = 'flex justify-between items-start p-3 bg-gray-50 rounded-lg';
            questionDiv.innerHTML = `
                <div class="flex-1">
                    <p class="text-sm font-medium text-gray-900">${questionText}</p>
                    <p class="text-xs text-gray-500">${questionType}</p>
                </div>
                <div class="flex items-center space-x-2">
                    <input type="number" value="5" class="w-16 border-gray-300 rounded text-sm" onchange="updateScore()">
                    <span class="text-xs text-gray-500">分</span>
                    <button onclick="removeFromExam(this)" class="text-red-600 hover:text-red-800 text-sm">×</button>
                </div>
            `;
            
            const container = document.getElementById('examQuestions');
            if (container.children[0].classList.contains('text-center')) {
                container.innerHTML = '';
            }
            container.appendChild(questionDiv);
            
            updateScore();
        }

        function removeFromExam(button) {
            button.closest('.flex').remove();
            updateScore();
        }

        function updateScore() {
            const scoreInputs = document.querySelectorAll('#examQuestions input[type="number"]');
            totalScore = Array.from(scoreInputs).reduce((sum, input) => sum + parseInt(input.value || 0), 0);
            document.getElementById('totalScore').textContent = totalScore;
        }

        function publishExam() {
            if (examQuestions.length === 0 && document.getElementById('examQuestions').children.length === 1) {
                alert('请先添加题目到试卷中');
                return;
            }
            alert('考试发布成功！参训者将收到考试通知。');
            window.location.href = 'training-management.html';
        }
    </script>
</body>
</html>