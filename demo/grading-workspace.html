<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>评卷工作台</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-50">
    <nav class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <h1 class="text-xl font-semibold text-gray-900">评卷工作台 - 新入职检察官办案指引考试</h1>
                </div>
                <div class="flex items-center space-x-4">
                    <button onclick="window.location.href='reviewer-dashboard.html'" class="text-gray-600 hover:text-gray-800">返回</button>
                    <span class="text-gray-700">李老师</span>
                    <button onclick="logout()" class="text-red-600 hover:text-red-800">退出</button>
                </div>
            </div>
        </div>
    </nav>

    <div class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div class="grid grid-cols-4 gap-6">
            <!-- 左侧：待评分答卷列表 -->
            <div class="col-span-1 bg-white shadow sm:rounded-lg">
                <div class="px-4 py-5 sm:p-6">
                    <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">待评分答卷 (15)</h3>
                    <div class="space-y-2 max-h-96 overflow-y-auto">
                        <div class="p-3 border rounded-lg cursor-pointer hover:bg-gray-50 bg-blue-50 border-blue-200" onclick="loadAnswer(1)">
                            <p class="text-sm font-medium">王萌</p>
                            <p class="text-xs text-gray-500">客观题：85分</p>
                            <p class="text-xs text-red-500">主观题：待评分</p>
                        </div>
                        <div class="p-3 border rounded-lg cursor-pointer hover:bg-gray-50" onclick="loadAnswer(2)">
                            <p class="text-sm font-medium">张三</p>
                            <p class="text-xs text-gray-500">客观题：78分</p>
                            <p class="text-xs text-red-500">主观题：待评分</p>
                        </div>
                        <div class="p-3 border rounded-lg cursor-pointer hover:bg-gray-50" onclick="loadAnswer(3)">
                            <p class="text-sm font-medium">李四</p>
                            <p class="text-xs text-gray-500">客观题：92分</p>
                            <p class="text-xs text-red-500">主观题：待评分</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 中间：题目和答案显示 -->
            <div class="col-span-2 bg-white shadow sm:rounded-lg">
                <div class="px-4 py-5 sm:p-6">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-lg leading-6 font-medium text-gray-900">答卷详情 - 王萌</h3>
                        <div class="text-sm text-gray-500">
                            进度：<span id="progress" class="font-medium text-gray-900">1/5</span>
                        </div>
                    </div>

                    <div id="questionContent" class="space-y-6">
                        <div class="border rounded-lg p-4">
                            <div class="mb-4">
                                <h4 class="font-medium text-gray-900 mb-2">题目 1 (20分)</h4>
                                <p class="text-sm text-gray-700">
                                    请结合《人民检察院刑事诉讼规则》，详细阐述检察官在审查起诉阶段的主要职责和工作流程，并分析如何确保案件质量。
                                </p>
                            </div>
                            
                            <div class="mb-4">
                                <h5 class="font-medium text-gray-900 mb-2">考生答案：</h5>
                                <div class="bg-gray-50 p-3 rounded border">
                                    <p class="text-sm text-gray-700">
                                        检察官在审查起诉阶段的主要职责包括：<br><br>
                                        1. 全面审查案件材料，确保犯罪事实清楚、证据确实充分；<br>
                                        2. 准确认定犯罪性质和罪名，适用法律正确；<br>
                                        3. 审查是否遗漏犯罪和其他应当追究刑事责任的人；<br>
                                        4. 对于证据不足的案件，应当退回补充侦查或作出不起诉决定；<br>
                                        5. 严格按照法定程序和时限要求办理案件。<br><br>
                                        工作流程方面，应当按照接收案件、审查材料、讯问犯罪嫌疑人、听取辩护人意见、作出决定等步骤进行。确保案件质量需要建立完善的内部监督机制和质量评查制度。
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="flex justify-between mt-6">
                        <button onclick="prevQuestion()" class="bg-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-400">
                            上一题
                        </button>
                        <button onclick="nextQuestion()" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">
                            下一题
                        </button>
                    </div>
                </div>
            </div>

            <!-- 右侧：评分区域 -->
            <div class="col-span-1 bg-white shadow sm:rounded-lg">
                <div class="px-4 py-5 sm:p-6">
                    <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">评分</h3>
                    
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">得分</label>
                            <div class="flex items-center space-x-2">
                                <input type="number" id="score" min="0" max="20" class="w-20 border-gray-300 rounded-md" placeholder="0">
                                <span class="text-sm text-gray-500">/ 20分</span>
                            </div>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">评语</label>
                            <textarea id="comment" rows="6" class="w-full border-gray-300 rounded-md" placeholder="请输入评语..."></textarea>
                        </div>
                        
                        <div class="space-y-2">
                            <button onclick="saveScore()" class="w-full bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700">
                                保存评分
                            </button>
                            <button onclick="submitAllScores()" class="w-full bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">
                                提交全部评分
                            </button>
                        </div>
                    </div>

                    <!-- 评分标准参考 -->
                    <div class="mt-6 p-3 bg-gray-50 rounded-lg">
                        <h4 class="font-medium text-gray-900 mb-2">评分标准</h4>
                        <div class="text-xs text-gray-600 space-y-1">
                            <p>• 18-20分：回答完整准确，逻辑清晰</p>
                            <p>• 15-17分：回答基本正确，有少量遗漏</p>
                            <p>• 12-14分：回答部分正确，有明显不足</p>
                            <p>• 8-11分：回答不够准确，错误较多</p>
                            <p>• 0-7分：回答错误或严重不足</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="js/auth.js"></script>
    <script>
        let currentQuestion = 1;
        let currentStudent = 1;
        const totalQuestions = 5;

        function loadAnswer(studentId) {
            currentStudent = studentId;
            // 模拟加载学生答案
            console.log('加载学生', studentId, '的答案');
        }

        function prevQuestion() {
            if (currentQuestion > 1) {
                currentQuestion--;
                updateProgress();
            }
        }

        function nextQuestion() {
            if (currentQuestion < totalQuestions) {
                currentQuestion++;
                updateProgress();
            }
        }

        function updateProgress() {
            document.getElementById('progress').textContent = `${currentQuestion}/${totalQuestions}`;
        }

        function saveScore() {
            const score = document.getElementById('score').value;
            const comment = document.getElementById('comment').value;
            
            if (!score) {
                alert('请输入分数');
                return;
            }
            
            alert('评分已保存');
            // 清空输入框
            document.getElementById('score').value = '';
            document.getElementById('comment').value = '';
        }

        function submitAllScores() {
            if (confirm('确认提交全部评分？提交后将无法修改。')) {
                alert('评分提交成功！成绩已发布给考生。');
                window.location.href = 'reviewer-dashboard.html';
            }
        }
    </script>
</body>
</html>