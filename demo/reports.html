<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>统计报表</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body class="bg-gray-50">
    <nav class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center space-x-8">
                    <h1 class="text-xl font-semibold text-gray-900">统计报表</h1>
                    <nav class="flex space-x-8">
                        <a href="initiator-dashboard.html" class="text-gray-500 hover:text-gray-700">首页</a>
                        <a href="training-management.html" class="text-gray-500 hover:text-gray-700">培训管理</a>
                        <a href="question-bank.html" class="text-gray-500 hover:text-gray-700">题库管理</a>
                        <a href="reports.html" class="text-blue-600 border-b-2 border-blue-600">统计报表</a>
                    </nav>
                </div>
                <div class="flex items-center space-x-4">
                    <span class="text-gray-700">张科长</span>
                    <button onclick="logout()" class="text-red-600 hover:text-red-800">退出</button>
                </div>
            </div>
        </div>
    </nav>

    <div class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <!-- 报表筛选 -->
        <div class="bg-white shadow sm:rounded-lg mb-6">
            <div class="px-4 py-5 sm:p-6">
                <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">报表筛选</h3>
                <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700">培训项目</label>
                        <select class="mt-1 block w-full border-gray-300 rounded-md">
                            <option>新入职检察官办案指引培训</option>
                            <option>2024年法规政策学习</option>
                            <option>全部项目</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">时间范围</label>
                        <select class="mt-1 block w-full border-gray-300 rounded-md">
                            <option>最近一周</option>
                            <option>最近一月</option>
                            <option>最近三月</option>
                            <option>自定义</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">部门</label>
                        <select class="mt-1 block w-full border-gray-300 rounded-md">
                            <option>全部部门</option>
                            <option>政治部</option>
                            <option>业务部门</option>
                            <option>行政部门</option>
                        </select>
                    </div>
                    <div class="flex items-end">
                        <button class="w-full bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">
                            生成报表
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 关键指标 -->
        <div class="grid grid-