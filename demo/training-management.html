<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>培训管理</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-50">
    <nav class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center space-x-8">
                    <h1 class="text-xl font-semibold text-gray-900">培训管理</h1>
                    <nav class="flex space-x-8">
                        <a href="initiator-dashboard.html" class="text-gray-500 hover:text-gray-700">首页</a>
                        <a href="training-management.html" class="text-blue-600 border-b-2 border-blue-600">培训管理</a>
                        <a href="question-bank.html" class="text-gray-500 hover:text-gray-700">题库管理</a>
                        <a href="reports.html" class="text-gray-500 hover:text-gray-700">统计报表</a>
                    </nav>
                </div>
                <div class="flex items-center space-x-4">
                    <span class="text-gray-700">张科长</span>
                    <button onclick="logout()" class="text-red-600 hover:text-red-800">退出</button>
                </div>
            </div>
        </div>
    </nav>

    <div class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <!-- 创建培训表单 -->
        <div class="bg-white shadow sm:rounded-lg mb-6">
            <div class="px-4 py-5 sm:p-6">
                <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">发起新培训</h3>
                <form class="space-y-4">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700">培训名称</label>
                            <input type="text" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500" placeholder="请输入培训名称">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">培训类型</label>
                            <select class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                                <option>新员工培训</option>
                                <option>法规政策学习</option>
                                <option>业务技能提升</option>
                                <option>其他</option>
                            </select>
                        </div>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">培训目标</label>
                        <textarea rows="3" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500" placeholder="请描述培训目标和要求"></textarea>
                    </div>
                    <div class="flex justify-end">
                        <button type="button" onclick="showStageDesigner()" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">
                            下一步：设计培训阶段
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- 阶段设计器 -->
        <div id="stageDesigner" class="bg-white shadow sm:rounded-lg hidden">
            <div class="px-4 py-5 sm:p-6">
                <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">培训阶段设计</h3>
                <div class="space-y-4">
                    <div class="border-2 border-dashed border-gray-300 rounded-lg p-6">
                        <div class="text-center">
                            <span class="text-gray-400 text-4xl">📚</span>
                            <h4 class="mt-2 text-lg font-medium text-gray-900">阶段 1: 学习阶段</h4>
                            <p class="text-sm text-gray-500">上传学习资料，设定学习要求</p>
                            <div class="mt-4 space-y-2">
                                <input type="file" class="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100">
                                <input type="date" class="block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500" placeholder="学习截止日期">
                            </div>
                        </div>
                    </div>
                    
                    <div class="border-2 border-dashed border-gray-300 rounded-lg p-6">
                        <div class="text-center">
                            <span class="text-gray-400 text-4xl">📝</span>
                            <h4 class="mt-2 text-lg font-medium text-gray-900">阶段 2: 考试阶段</h4>
                            <p class="text-sm text-gray-500">创建考试，设定考试参数</p>
                            <div class="mt-4 grid grid-cols-2 gap-2">
                                <input type="datetime-local" class="block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500" placeholder="考试开始时间">
                                <input type="number" class="block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500" placeholder="考试时长(分钟)">
                            </div>
                            <button type="button" onclick="window.location.href='exam-creator.html'" class="mt-2 bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700">
                                创建考试
                            </button>
                        </div>
                    </div>
                </div>
                
                <div class="mt-6 flex justify-between">
                    <button type="button" class="bg-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-400">
                        添加阶段
                    </button>
                    <button type="button" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">
                        发布培训
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="js/auth.js"></script>
    <script>
        function showStageDesigner() {
            document.getElementById('stageDesigner').classList.remove('hidden');
        }
    </script>
</body>
</html>