<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>培训发起者 - 首页</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-50">
    <!-- 导航栏 -->
    <nav class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <h1 class="text-xl font-semibold text-gray-900">AI智能培训系统</h1>
                </div>
                <div class="flex items-center space-x-4">
                    <span class="text-gray-700">欢迎，张科长</span>
                    <button onclick="logout()" class="text-red-600 hover:text-red-800">退出</button>
                </div>
            </div>
        </div>
    </nav>

    <div class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <!-- 快速操作卡片 -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <div class="bg-white overflow-hidden shadow rounded-lg cursor-pointer hover:shadow-md transition-shadow" onclick="window.location.href='training-management.html'">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                                <span class="text-white text-sm">📚</span>
                            </div>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">发起新培训</dt>
                                <dd class="text-lg font-medium text-gray-900">创建培训项目</dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-white overflow-hidden shadow rounded-lg cursor-pointer hover:shadow-md transition-shadow" onclick="window.location.href='question-bank.html'">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                                <span class="text-white text-sm">🤖</span>
                            </div>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">AI题目生成</dt>
                                <dd class="text-lg font-medium text-gray-900">智能生成题库</dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-white overflow-hidden shadow rounded-lg cursor-pointer hover:shadow-md transition-shadow" onclick="window.location.href='reports.html'">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center">
                                <span class="text-white text-sm">📊</span>
                            </div>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">统计报表</dt>
                                <dd class="text-lg font-medium text-gray-900">查看培训效果</dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 培训项目列表 -->
        <div class="bg-white shadow overflow-hidden sm:rounded-md">
            <div class="px-4 py-5 sm:px-6 flex justify-between items-center">
                <h3 class="text-lg leading-6 font-medium text-gray-900">我的培训项目</h3>
                <button onclick="window.location.href='training-management.html'" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">
                    发起新培训
                </button>
            </div>
            <ul class="divide-y divide-gray-200">
                <li class="px-4 py-4 hover:bg-gray-50 cursor-pointer" onclick="window.location.href='training-detail.html?id=1'">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="flex-1 min-w-0">
                                <p class="text-sm font-medium text-gray-900 truncate">新入职检察官办案指引培训</p>
                                <p class="text-sm text-gray-500">创建时间：2024-01-15 | 参与人数：25人</p>
                            </div>
                        </div>
                        <div class="flex items-center space-x-2">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                进行中
                            </span>
                        </div>
                    </div>
                </li>
                <li class="px-4 py-4 hover:bg-gray-50 cursor-pointer">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="flex-1 min-w-0">
                                <p class="text-sm font-medium text-gray-900 truncate">2024年法规政策学习</p>
                                <p class="text-sm text-gray-500">创建时间：2024-01-10 | 参与人数：120人</p>
                            </div>
                        </div>
                        <div class="flex items-center space-x-2">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                已结束
                            </span>
                        </div>
                    </div>
                </li>
            </ul>
        </div>
    </div>

    <script src="js/auth.js"></script>
    <script>
        // 检查登录状态
        const user = checkAuth();
        if (!user || user.role !== 'initiator') {
            window.location.href = 'index.html';
        }
    </script>
</body>
</html>