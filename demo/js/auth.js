// 模拟用户数据
const users = {
    admin: { name: '系统管理员', role: 'admin' },
    initiator: { name: '张科长', role: 'initiator' },
    reviewer: { name: '李老师', role: 'reviewer' },
    trainee: { name: '王萌', role: 'trainee' }
};

function handleLogin(event) {
    event.preventDefault();
    
    const username = document.getElementById('username').value;
    const password = document.getElementById('password').value;
    const role = document.getElementById('role').value;
    
    // 简单验证
    if (username && password) {
        // 存储用户信息
        localStorage.setItem('currentUser', JSON.stringify(users[role]));
        
        // 根据角色跳转到对应首页
        switch(role) {
            case 'admin':
                window.location.href = 'admin-dashboard.html';
                break;
            case 'initiator':
                window.location.href = 'initiator-dashboard.html';
                break;
            case 'reviewer':
                window.location.href = 'reviewer-dashboard.html';
                break;
            case 'trainee':
                window.location.href = 'trainee-dashboard.html';
                break;
        }
    } else {
        alert('请输入用户名和密码');
    }
}

// 检查登录状态
function checkAuth() {
    const user = localStorage.getItem('currentUser');
    if (!user) {
        window.location.href = 'index.html';
        return null;
    }
    return JSON.parse(user);
}

// 退出登录
function logout() {
    localStorage.removeItem('currentUser');
    window.location.href = 'index.html';
}